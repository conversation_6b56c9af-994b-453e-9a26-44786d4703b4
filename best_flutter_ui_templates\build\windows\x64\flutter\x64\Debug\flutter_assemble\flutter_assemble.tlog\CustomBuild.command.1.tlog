^D:\软件工具类\GITHUB DESKTOP\BEST-FLUTTER-UI-TEMPLATES\BEST_FLUTTER_UI_TEMPLATES\BUILD\WINDOWS\X64\CMAKEFILES\E65B91C0852A22E7A3A5B0DF6E951786\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\Users\<USER>\AppData\Local\dev\flutter "PROJECT_DIR=D:\软件工具类\GitHub Desktop\Best-Flutter-UI-Templates\best_flutter_ui_templates" FLUTTER_ROOT=C:\Users\<USER>\AppData\Local\dev\flutter "FLUTTER_EPHEMERAL_DIR=D:\软件工具类\GitHub Desktop\Best-Flutter-UI-Templates\best_flutter_ui_templates\windows\flutter\ephemeral" "PROJECT_DIR=D:\软件工具类\GitHub Desktop\Best-Flutter-UI-Templates\best_flutter_ui_templates" "FLUTTER_TARGET=D:\软件工具类\GitHub Desktop\Best-Flutter-UI-Templates\best_flutter_ui_templates\lib\main.dart" DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false "PACKAGE_CONFIG=D:\软件工具类\GitHub Desktop\Best-Flutter-UI-Templates\best_flutter_ui_templates\.dart_tool\package_config.json" C:/Users/<USER>/AppData/Local/dev/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\软件工具类\GITHUB DESKTOP\BEST-FLUTTER-UI-TEMPLATES\BEST_FLUTTER_UI_TEMPLATES\BUILD\WINDOWS\X64\CMAKEFILES\783B5CA51A5754CD9A23116ED995EB2D\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\软件工具类\GITHUB DESKTOP\BEST-FLUTTER-UI-TEMPLATES\BEST_FLUTTER_UI_TEMPLATES\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SD:/软件工具类/GitHub Desktop/Best-Flutter-UI-Templates/best_flutter_ui_templates/windows" "-BD:/软件工具类/GitHub Desktop/Best-Flutter-UI-Templates/best_flutter_ui_templates/build/windows/x64" --check-stamp-file "D:/软件工具类/GitHub Desktop/Best-Flutter-UI-Templates/best_flutter_ui_templates/build/windows/x64/flutter/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
