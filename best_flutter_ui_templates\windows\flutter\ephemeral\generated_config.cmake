# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\AppData\\Local\\dev\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\软件工具类\\GitHub Desktop\\Best-Flutter-UI-Templates\\best_flutter_ui_templates" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\AppData\\Local\\dev\\flutter"
  "PROJECT_DIR=D:\\软件工具类\\GitHub Desktop\\Best-Flutter-UI-Templates\\best_flutter_ui_templates"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\AppData\\Local\\dev\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\软件工具类\\GitHub Desktop\\Best-Flutter-UI-Templates\\best_flutter_ui_templates\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\软件工具类\\GitHub Desktop\\Best-Flutter-UI-Templates\\best_flutter_ui_templates"
  "FLUTTER_TARGET=D:\\软件工具类\\GitHub Desktop\\Best-Flutter-UI-Templates\\best_flutter_ui_templates\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\软件工具类\\GitHub Desktop\\Best-Flutter-UI-Templates\\best_flutter_ui_templates\\.dart_tool\\package_config.json"
)
